package organviz

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/coreapi/pkg/coreapi"
	"gitlab.com/pockethealth/coreapi/pkg/reports/organviz/orientation"
	"gitlab.com/pockethealth/coreapi/pkg/services/reportinsights"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type OrganVizCTMedsam struct {
	riClient reportinsights.ReportInsights
}

func NewOrganVizCTMedsam(riClient reportinsights.ReportInsights) *OrganVizCTMedsam {
	return &OrganVizCTMedsam{
		riClient: riClient,
	}
}

func (s *OrganVizCTMedsam) FetchVisualizations(ctx context.Context, examId string) ([]byte, error) {
	query := map[string]string{"model": "ct_abd", "exam_id": examId}
	return s.riClient.GetOrganVisualizationForExam(ctx, examId, query)
}

func isExamEligibleForCT(exam coreapi.ExamRawBasic) (ExamEligibility, error) {
	age, err := patientAgeAtTimeOfExam(exam)
	if err != nil {
		return UNKNOWN, fmt.Errorf("unable to get patient age at time of exam: %v", err)
	}
	if age < 18 {
		return INELIGIBLE_AGE, nil
	}
	if !bodyPartMatches(exam, "abd", "chest", "thorax") {
		return INELIGIBLE_BODY_PART, nil
	}
	return ELIGIBLE_FOR_MEDSAM, nil
}

func isCTScan(exam coreapi.ExamRawBasic) bool {
	haystack := strings.ToLower(exam.Modality)
	if exam.Modality == "" {
		haystack = strings.ToLower(exam.Description)
	}

	return strings.Contains(haystack, "ct") || strings.Contains(haystack, "cat")
}

func (s *Client) selectValidSeriesForMedSAM(ctx context.Context, examUuid string) (*string, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"exam_uuid": examUuid,
	})

	series, err := s.getSeriesInExam(ctx, examUuid)
	if err != nil {
		return nil, fmt.Errorf("failed getting series in exam %v: %w", examUuid, err)
	}
	if len(series) == 0 {
		// no valid series found, but also no specific errors. there simply weren't any
		// valid series to select after checking series description
		return nil, nil
	}

	// To increase likelihood of giving the organviz model varied images to work
	// on we want to select the series with the most images in it
	err = s.sortSeriesByImgCountDesc(ctx, series)
	if err != nil {
		return nil, fmt.Errorf("failed sorting series in exam %v: %w", examUuid, err)
	}

	numValidationErrors := 0
	for _, seriesUid := range series {
		sd, err := s.getSeriesData(ctx, examUuid, seriesUid)
		if err != nil {
			numValidationErrors++
			lg.WithField("seriesUid", seriesUid).WithError(err).Info("failed fetching series data")
			continue
		}

		if sd.orientation != orientation.AXIAL {
			continue
		}

		if sd.sopClass == secondaryCaptureImageStorage {
			// Objects from "Secondary Capture Image Storage" are unlikely to be
			// scanned images, and more likely to be reports and other types of
			// data. Organviz only works on scanned images, so ignore any series
			// with this SOP class. See AIT-84
			continue
		}

		return &seriesUid, nil
	}
	if numValidationErrors > 0 && numValidationErrors == len(series) {
		return nil, fmt.Errorf("all series in report %v encountered errors during validation", examUuid)
	}

	// no series found, but also no specific errors. there simply weren't any
	// valid series to select
	return nil, nil
}
