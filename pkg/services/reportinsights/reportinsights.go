package reportinsights

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v8/pkg/logutils"
)

type InsightsClient struct {
	BaseURL    string
	HTTPClient *httpclient.Client
}
type ReportInsights interface {
	GetInsights(ctx context.Context, reportIds []string) (InsightsResponse, error)
	GetFollowups(ctx context.Context, reportIds []string) (map[string]FollowUpsResp, error)
	GetOrganVisualization(ctx context.Context, reportId string, queryParams map[string]string) ([]byte, error)
	GetOrganVisualizationForExam(ctx context.Context, examId string, queryParams map[string]string) ([]byte, error)
}

// NewInsightsClient constructs a new InsightsClient with the given URL and HTTP client
func NewInsightsClient(baseURL string, client *httpclient.Client) *InsightsClient {
	if client == nil {
		client = httpclient.NewHTTPClient(&http.Client{}, nil)
	}

	return &InsightsClient{
		BaseURL:    baseURL,
		HTTPClient: client,
	}
}

func (ri *InsightsClient) GetInsights(ctx context.Context, reportIds []string) (InsightsResponse, error) {
	lg := logutils.DebugCtxLogger(ctx)
	reqBodyJson, err := json.Marshal(map[string]any{"report_ids": reportIds})
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return InsightsResponse{}, err
	}

	endpoint := fmt.Sprintf("%s/v1/insights", ri.BaseURL)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		ReqBody:       reqBodyJson,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Bearer,
		ExpectedCodes: []int{http.StatusOK, http.StatusBadRequest},
	}

	respBody, code, err := ri.HTTPClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf("error sending request to report insight")
		return InsightsResponse{}, err
	}

	if code != http.StatusOK {
		return InsightsResponse{}, fmt.Errorf("got %v back from report insights", code)
	}

	body, err := io.ReadAll(respBody)
	if err != nil {
		lg.WithError(err).Error("unable to read response body")
		return InsightsResponse{}, err
	}

	var result InsightsResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		lg.WithError(err).Error("unable to parse response body")
		return InsightsResponse{}, err
	}

	return result, nil
}

func (ri *InsightsClient) GetFollowups(ctx context.Context, reportIds []string) (map[string]FollowUpsResp, error) {
	lg := logutils.DebugCtxLogger(ctx)
	reqBodyJson, err := json.Marshal(map[string]any{"report_ids": reportIds})
	if err != nil {
		lg.WithError(err).Error("error encoding request body as json")
		return nil, fmt.Errorf("failed encoding request body as json: %w", err)
	}

	endpoint := fmt.Sprintf("%s/v1/followup/bulk/read", ri.BaseURL)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		ReqBody:       reqBodyJson,
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Bearer,
		ExpectedCodes: []int{http.StatusOK},
	}

	respBody, _, err := ri.HTTPClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf("error sending request to reportinsights")
		return nil, fmt.Errorf("failed sending request to reportinsights: %w", err)
	}

	var batchResponse FollowupBatchResponse
	err = json.NewDecoder(respBody).Decode(&batchResponse)
	if err != nil {
		lg.WithError(err).Errorf("error parsing reportinsights response")
		return nil, fmt.Errorf("failed parsing reportinisghts response: %w", err)
	}

	// transform response and send it back
	transformedResponse := make(map[string]FollowUpsResp)
	for _, reportResponse := range batchResponse {
		transformedResponse[reportResponse.ReportId] = reportResponse.Followups
	}
	return transformedResponse, nil
}

func (ri *InsightsClient) GetOrganVisualization(ctx context.Context, reportId string, queryParams map[string]string) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx)

	endpoint := fmt.Sprintf("%s/v1/organviz/%s", ri.BaseURL, reportId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Bearer,
		QueryParams:   queryParams,
		ExpectedCodes: []int{http.StatusOK, http.StatusBadRequest, http.StatusUnauthorized},
	}

	resp, statusCode, err := ri.HTTPClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf("error sending organviz request to report insight")
		return nil, err
	}

	if statusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from report insights", statusCode)
	}

	return io.ReadAll(resp)
}

func (ri *InsightsClient) GetOrganVisualizationForExam(ctx context.Context, examId string, queryParams map[string]string) ([]byte, error) {
	lg := logutils.DebugCtxLogger(ctx)

	endpoint := fmt.Sprintf("%s/v1/organviz/exam/%s", ri.BaseURL, examId)
	reqParams := httpclient.RequestParameters{
		HTTPMethod:    "POST",
		TargetURL:     endpoint,
		AuthScheme:    httpclient.Bearer,
		QueryParams:   queryParams,
		ExpectedCodes: []int{http.StatusOK, http.StatusBadRequest, http.StatusUnauthorized},
	}

	resp, statusCode, err := ri.HTTPClient.SendRequest(ctx, reqParams)
	if err != nil {
		lg.WithError(err).Errorf("error sending organviz request to report insight")
		return nil, err
	}

	if statusCode != http.StatusOK {
		return nil, fmt.Errorf("got %v back from report insights", statusCode)
	}

	return io.ReadAll(resp)
}
