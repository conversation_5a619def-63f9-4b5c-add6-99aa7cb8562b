package reportinsights

import (
	"context"

	"gitlab.com/pockethealth/phutils/v10/pkg/testutils"
)

type ReportInsightsMock struct {
	testutils.BaseMock
}

func NewReportInsightsMock() *ReportInsightsMock {
	return &ReportInsightsMock{
		BaseMock: *testutils.NewBaseMock(),
	}
}

func (mock *ReportInsightsMock) GetInsights(
	ctx context.Context,
	reportIds []string,
) (InsightsResponse, error) {
	mock.IncrementCounter("GetInsights")
	result := testutils.ReturnMockValueOrDefault(mock.Returns, "GetInsights", struct {
		Value InsightsResponse
		Err   error
	}{Value: InsightsResponse{}, Err: nil})
	return result.Value, result.Err
}

func (mock *ReportInsightsMock) GetFollowups(ctx context.Context, reportIds []string) (map[string]FollowUpsResp, error) {
	mock.IncrementCounter("GetFollowups")
	result := testutils.ReturnMockValueOrDefault(mock.Returns, "GetFollowups", struct {
		Value map[string]FollowUpsResp
		Err   error
	}{Value: nil, Err: nil})
	return result.Value, result.Err
}

func (mock *ReportInsightsMock) GetOrganVisualization(ctx context.Context, reportId string, queryParams map[string]string) ([]byte, error) {
	mock.IncrementCounter("GetOrganVisualization")

	result := testutils.ReturnMockValueOrDefault(mock.Returns, "GetOrganVisualization", struct {
		Value []byte
		Err   error
	}{Value: []byte(`{
		"organs": [{
			"segmentation": "some segments",
			"body_part": "liver",
			"status": "COMPLETED"
		}]
	}`), Err: nil})

	switch queryParams["model"] {
	case "ct_abd":
		result.Value = []byte(`{
			"organs": [{
				"segmentation": "some segments",
				"body_part": "spleen",
				"status": "COMPLETED"
			}]
		}`)
	case "xray_chest":
		result.Value = []byte(`{
			"organs": [{
				"segmentation": "some segments",
				"body_part": "left lung",
				"status": "COMPLETED"
			}]
		}`)
	case "mri_abd":
		result.Value = []byte(`{
			"organs": [{
				"segmentation": "some segments",
				"body_part": "right lung",
				"status": "COMPLETED"
			}]
		}`)
	}
	return result.Value, result.Err
}

func (mock *ReportInsightsMock) GetOrganVisualizationForExam(ctx context.Context, examId string, queryParams map[string]string) ([]byte, error) {
	mock.IncrementCounter("GetOrganVisualizationForExam")

	result := testutils.ReturnMockValueOrDefault(mock.Returns, "GetOrganVisualizationForExam", struct {
		Value []byte
		Err   error
	}{Value: []byte(`{
		"organs": [{
			"segmentation": "some segments",
			"body_part": "liver",
			"status": "COMPLETED"
		}]
	}`), Err: nil})

	switch queryParams["model"] {
	case "ct_abd":
		result.Value = []byte(`{
			"organs": [{
				"segmentation": "some segments",
				"body_part": "spleen",
				"status": "COMPLETED"
			}]
		}`)
	case "xray_chest":
		result.Value = []byte(`{
			"organs": [{
				"segmentation": "some segments",
				"body_part": "left lung",
				"status": "COMPLETED"
			}]
		}`)
	case "mri_abd":
		result.Value = []byte(`{
			"organs": [{
				"segmentation": "some segments",
				"body_part": "right lung",
				"status": "COMPLETED"
			}]
		}`)
	}
	return result.Value, result.Err
}
